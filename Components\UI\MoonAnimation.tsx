import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

// 🔧 CISCO: SYSTÈME DE CONTINUITÉ LUNE - POSITIONS SIMPLIFIÉES
// Sauvegarde des positions entre modes pour continuité parfaite
let savedMoonPosition = { left: '5%', top: '75%', opacity: 0 };

// 🔧 CISCO: FONCTIONS DE SAUVEGARDE/RÉCUPÉRATION POSITION LUNE
const saveMoonPosition = (element: HTMLElement) => {
  const currentLeft = gsap.getProperty(element, "left") as string;
  const currentTop = gsap.getProperty(element, "top") as string;
  const currentOpacity = gsap.getProperty(element, "opacity") as number;
  savedMoonPosition = {
    left: currentLeft,
    top: currentTop,
    opacity: currentOpacity
  };
  console.log(`💾 POSITION LUNE SAUVEGARDÉE: left=${currentLeft}, top=${currentTop}, opacity=${currentOpacity}`);
};

const getSavedMoonPosition = () => {
  console.log(`📥 RÉCUPÉRATION POSITION LUNE: left=${savedMoonPosition.left}, top=${savedMoonPosition.top}, opacity=${savedMoonPosition.opacity}`);
  return savedMoonPosition;
};

interface MoonAnimationProps {
  currentPhase: string; // Phase cinématographique actuelle (aube/midi/coucher/nuit)
}

const MoonAnimation: React.FC<MoonAnimationProps> = ({ currentPhase }) => {
  const moonRef = useRef<HTMLDivElement>(null);
  const haloRef = useRef<HTMLDivElement>(null); // 🔧 CISCO: Référence séparée pour le halo
  const animationRef = useRef<gsap.core.Timeline | null>(null);
  const fadeOutRef = useRef<gsap.core.Timeline | null>(null); // 🔧 CISCO: Correction type Timeline
  const isAnimatingRef = useRef<boolean>(false); // 🔧 CISCO: Protection contre les déclenchements multiples
  const hasAnimatedRef = useRef<boolean>(false); // 🔧 CISCO: Empêcher les re-animations

  // 🔧 CISCO: Références pour le cycle automatique
  const automaticCycleRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!moonRef.current || !haloRef.current) return;

    // 🔧 CISCO: DÉBOGAGE - Tracer tous les déclenchements du useEffect
    console.log(`🌙 MoonAnimation useEffect déclenché: currentPhase=${currentPhase}, isAnimating=${isAnimatingRef.current}`);

    // 🌙 CISCO: LOGIQUE CORRIGÉE - Lune se lève à la nuit, se couche à l'aube
    if (currentPhase === 'nuit' || currentPhase === 'night') {
      // 🌙 NUIT: Lune se lève et parcourt sa trajectoire complète
      console.log(`🌙 NUIT détectée: ${currentPhase} - Démarrage trajectoire complète lever→zénith→coucher`);

      // 🔧 CISCO: PROTECTION RENFORCÉE - Éviter les déclenchements multiples
      if (isAnimatingRef.current) {
        console.log('🌙 Animation lune déjà en cours (protection renforcée) - éviter le redémarrage');
        return;
      }

      // 🔧 CISCO: Vérifier si l'animation GSAP est déjà active
      if (animationRef.current && animationRef.current.isActive()) {
        console.log('🌙 Animation GSAP lune déjà active - éviter le redémarrage');
        return;
      }

      console.log('🌙 DÉMARRAGE animation lune - NUIT: Trajectoire complète lever→zénith→coucher');

    } else if (currentPhase === 'aube' || currentPhase === 'dawn') {
      // 🌅 AUBE: Lune termine sa descente et disparaît derrière le paysage
      console.log(`🌅 AUBE détectée: ${currentPhase} - Lune termine sa descente et disparaît`);

      // 🔧 CISCO: PROTECTION RENFORCÉE - Éviter les déclenchements multiples
      if (isAnimatingRef.current) {
        console.log('🌙 Animation lune déjà en cours (protection renforcée) - éviter le redémarrage');
        return;
      }

      console.log('🌙 DÉMARRAGE animation lune - AUBE: Descente finale et disparition');

    } else {
      // 🌞 AUTRES PHASES: Lune complètement cachée
      console.log(`🌞 Phase diurne détectée: ${currentPhase} - Lune cachée`);

      // 🔧 CISCO: Libérer TOUS les verrous d'animation
      isAnimatingRef.current = false;
      hasAnimatedRef.current = false;

      // Arrêter toute animation en cours
      if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }

      // Cacher la lune immédiatement
      if (moonRef.current && haloRef.current) {
        gsap.set([moonRef.current, haloRef.current], {
          opacity: 0,
          display: 'none'
        });
      }
      return;
    }

    // 🔧 CISCO: Si on arrive ici, c'est une phase avec lune (nuit ou aube)

    // 🔧 CISCO: Marquer comme en cours d'animation
    isAnimatingRef.current = true;
    hasAnimatedRef.current = true;

    // Arrêter toute animation en cours
    if (animationRef.current) {
      animationRef.current.kill();
    }
    if (fadeOutRef.current) {
      fadeOutRef.current.kill();
    }

    // 🔧 CISCO: Timeline avec gestion des deux phases
    animationRef.current = gsap.timeline({
      onComplete: () => {
        console.log('🌙 Animation lune terminée - Libération du verrou');
        isAnimatingRef.current = false;
      }
    });

    if (currentPhase === 'nuit' || currentPhase === 'night') {
      // 🌙 NUIT: Trajectoire complète lever → zénith → coucher
      console.log('🌙 NUIT: Démarrage trajectoire complète (15 minutes)');

      // 🔧 CISCO: Position initiale simplifiée - POURCENTAGES SIMPLES
      gsap.set(moonRef.current, {
        left: '5%', top: '75%', // 🔧 CISCO: Pourcentages simples au lieu de vw/vh
        opacity: 0, scale: 1, display: 'block',
        transform: 'translate(-50%, -50%)' // 🔧 CISCO: Transform CSS direct
      });
      gsap.set(haloRef.current, {
        left: '5%', top: '75%', // 🔧 CISCO: Même position que la lune
        opacity: 0, display: 'block',
        transform: 'translate(-50%, -50%)' // 🔧 CISCO: Transform CSS direct
      });

      // Apparition progressive
      animationRef.current.to([moonRef.current, haloRef.current], {
        opacity: 1.0, // Lune
        duration: 60 // 🔧 CISCO: RALENTI pour mouvement contemplatif (1min)
      });
      animationRef.current.to(haloRef.current, {
        opacity: 0.25, // Halo plus subtil
        duration: 60 // 🔧 CISCO: RALENTI pour mouvement contemplatif (1min)
      }, 0);

      // 🔧 CISCO: Trajectoire parabolique simplifiée - CORRECTION left/top
      animationRef.current.to([moonRef.current, haloRef.current], {
        keyframes: [
          // Phase 1: Lever depuis derrière paysage vers zénith
          { left: '5%', top: '75%', duration: 0 },     // Départ derrière paysage
          { left: '15%', top: '55%', duration: 0.15 }, // Montée progressive
          { left: '25%', top: '35%', duration: 0.25 }, // Continue montée
          { left: '35%', top: '20%', duration: 0.35 }, // Approche zénith
          { left: '45%', top: '10%', duration: 0.45 }, // Proche zénith
          { left: '50%', top: '5%', duration: 0.5 },   // ZÉNITH (point le plus haut)
          // Phase 2: Descente vers paysage opposé
          { left: '55%', top: '10%', duration: 0.55 }, // Début descente
          { left: '65%', top: '20%', duration: 0.65 }, // Descente progressive
          { left: '75%', top: '35%', duration: 0.75 }, // Continue descente
          { left: '85%', top: '55%', duration: 0.85 }, // Approche paysage opposé
          { left: '95%', top: '75%', duration: 1.0 }   // Coucher derrière paysage opposé
        ],
        duration: 2400, // 🔧 CISCO: RALENTI pour mouvement contemplatif (40 minutes de trajectoire)
        ease: "power2.inOut",
        onComplete: () => {
          // 🔧 CISCO: SAUVEGARDER POSITION pour continuité avec mode AUBE
          if (moonRef.current) {
            saveMoonPosition(moonRef.current);
            console.log('🌙 NUIT terminée - Position lune sauvegardée pour récupération par mode AUBE');
          }
        }
      }, 60); // Commence après l'apparition (1min)

    } else if (currentPhase === 'aube' || currentPhase === 'dawn') {
      // 🌅 AUBE: SYSTÈME CONTINUITÉ - Lune continue sa descente depuis position sauvegardée
      console.log('🌅 AUBE: SYSTÈME CONTINUITÉ - Récupération position lune depuis NUIT');

      // 🔧 CISCO: RÉCUPÉRER POSITION SAUVEGARDÉE pour continuité parfaite
      const savedPos = getSavedMoonPosition();

      // 🔧 CISCO: POSITIONNER D'ABORD à la position sauvegardée (pas de saut)
      gsap.set(moonRef.current, {
        left: savedPos.left,
        top: savedPos.top,
        opacity: savedPos.opacity,
        scale: 1,
        display: 'block',
        transform: 'translate(-50%, -50%)' // 🔧 CISCO: Transform CSS direct
      });
      gsap.set(haloRef.current, {
        left: savedPos.left,
        top: savedPos.top,
        opacity: savedPos.opacity * 0.3, // Halo plus subtil
        display: 'block',
        transform: 'translate(-50%, -50%)' // 🔧 CISCO: Transform CSS direct
      });

      console.log(`🌅 AUBE: Position récupérée: (${savedPos.left}, ${savedPos.top}) → Disparition derrière paysage`);

      // 🔧 CISCO: CONTINUITÉ PARFAITE - Continuer la descente depuis position sauvegardée
      animationRef.current.to([moonRef.current, haloRef.current], {
        keyframes: [
          { left: savedPos.left, top: savedPos.top, duration: 0 },     // Position sauvegardée
          { left: '90%', top: '70%', duration: 0.5 },   // Descente progressive
          { left: '95%', top: '85%', duration: 1.0 }    // Disparition derrière paysage
        ],
        duration: 900, // 🔧 CISCO: RALENTI pour mouvement contemplatif (15 minutes de descente)
        ease: "power2.inOut"
      });

      // Disparition progressive synchronisée
      animationRef.current.to([moonRef.current, haloRef.current], {
        opacity: 0,
        duration: 960, // 🔧 CISCO: RALENTI pour mouvement contemplatif (16 minutes total)
        ease: "power2.out"
      }, 0);
    }

    // 🔧 CISCO: Logique déjà gérée plus haut pour les phases diurnes

    // Nettoyage au démontage
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }
    };
  }, [currentPhase]);

  return (
    <>
      {/* 🌙 CISCO: Halo lumineux séparé pour éviter l'effet carré */}
      <div
        ref={haloRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 7, // 🔧 CISCO: Lune + Halo DERRIÈRE les nuages (z-index 7 < nuages z-index 9)
          display: 'none',
          width: '200px',
          height: '200px',
          background: 'radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 30%, rgba(255, 255, 255, 0.04) 60%, transparent 100%)', // 🔧 CISCO: Halo plus lumineux
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      />

      {/* 🌙 CISCO: Lune principale */}
      <div
        ref={moonRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 7, // 🔧 CISCO: Lune + Halo DERRIÈRE les nuages (z-index 7 < nuages z-index 9)
          display: 'none',
          width: '120px',
          height: '120px',
          backgroundImage: 'url(/Lune-Moon.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          filter: 'brightness(1.6) contrast(1.3)', // 🔧 CISCO: Plus lumineuse (était 1.3/1.1)
        }}
        title="🌙 Lune nocturne"
      />
    </>
  );
};

export default MoonAnimation;
