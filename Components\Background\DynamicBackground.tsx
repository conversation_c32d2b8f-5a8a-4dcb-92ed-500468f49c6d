import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import AstronomicalLayer from './AstronomicalLayer';
import DiurnalLayer from './DiurnalLayer';
// 🎬 CISCO: SUPPRESSION useDayCycleOptional - Remplacé par props directes du système Cinema
// 🎨 CISCO: IMPORT DES VRAIES PALETTES - Remplacement des couleurs pastels (SUPPRIMÉ - non utilisé)

// 🔧 CISCO: Système de rotation supprimé - Background fixe pour éviter les changements automatiques

// 🔧 CISCO: Fonction supprimée - Background fixe pour éviter les changements automatiques

// 🎬 CISCO: SYSTÈME CINÉMATOGRAPHIQUE SIMPLIFIÉ - 4 MODES UNIQUEMENT
// Types pour les modes de fond cinématographiques (correspondance avec CinemaPhase)
type BackgroundMode =
  | 'dawn'        // Aube (correspond à 'aube')
  | 'dawnPhase2'  // Aube Phase 2 (progression vers midi)
  | 'midday'      // Midi (correspond à 'midi')
  | 'sunset'      // Coucher (correspond à 'coucher')
  | 'night'       // Nuit (correspond à 'nuit')
  // 🔧 CISCO: Anciens modes pour compatibilité
  | 'sunrise' | 'aube' | 'morning' | 'afternoon' | 'midi' | 'dusk' | 'coucher' | 'nuit';

// 🔧 CISCO: MODES SUPPRIMÉS (simplification) : sunrise, morning, afternoon, dusk

// 🎨 CISCO: VRAIES PALETTES CISCO - Remplacement complet des couleurs pastels
// ✨ NOUVELLES COULEURS: Extraites des vraies captures d'écran de ciels naturels
// Dégradés fluides synchronisés avec le temporisateur de journée

// 🎬 CISCO: PALETTES CINÉMATOGRAPHIQUES SIMPLIFIÉES - 4 MODES ESSENTIELS
// Dégradés cohérents selon les règles d'or CISCO (nuages/paysage/arrière-plan)
const BACKGROUND_MODES = {
  // 🌅 === AUBE === 🌅
  // CISCO: VRAIES COULEURS !
  dawn: {
    primary: '#e2c5e7ff',    // Bas : Bleu très clair (horizon lumineux)
    secondary: '#2e446eff',  // Milieu : Bleu clair éclatant
    tertiary: '#182d47ff'    // Haut : Bleu pur intense
  },

  // 🌅 === AUBE PHASE 2 === 🌅
  // CISCO: DEUXIÈME DÉGRADÉ - Progression vers midi
  dawnPhase2: {
    primary: '#f5cc5aff',    // Bas : Plus lumineux que l'aube initiale
    secondary: '#6d81c4ff',  // Milieu : Bleu plus intense
    tertiary: '#355ea5ff'    // Haut : Bleu plus profond
  },

  // ☀️ === MIDI === ☀️
  // CISCO: CORRECTION CRITIQUE - Couleurs bleues renforcées pour éviter le blanc cassé
  midday: {
    primary: '#b8d8f1ff',    // Bas : Bleu clair mais visible (plus saturé que e3f2fd)
    secondary: '#68a9dfff',  // Milieu : Bleu vif éclatant (plus saturé que 90caf9)
    tertiary: '#368adfff'    // Haut : Bleu intense profond (plus saturé que 42a5f5)
  },

  // 🌇 === COUCHER === 🌇
  // CISCO: VRAIES COULEURS BLEUES - PAS D'ORANGE !
  sunset: {
    primary: '#bbdefb',    // Bas : Bleu très doux (horizon)
    secondary: '#64b5f6',  // Milieu : Bleu moyen
    tertiary: '#1976d2'    // Haut : Bleu profond
  },

  // 🌌 === NUIT === 🌌
  // CISCO: Lumière au minimum - Bleu nuit profond uniforme
  night: {
    primary: '#0d1117',    // Bas : Bleu nuit très sombre (horizon nocturne)
    secondary: '#1c2938',  // Milieu : Bleu nuit intermédiaire
    tertiary: '#2d3748'    // Haut : Bleu nuit plus clair (étoiles visibles)
  }
};

// 🔧 CISCO: TRANSITIONS AVEC VRAIES COULEURS - Ponts naturels entre les modes
// Ces transitions permettent de passer en douceur d'un mode à un autre en utilisant des couleurs intermédiaires.
// Les couleurs sont définies pour trois zones de l'écran :
// - `primary` : correspond à la partie basse de l'écran (proche de l'horizon, le plus clair).
// - `secondary` : correspond à la partie intermédiaire de l'écran (milieu du ciel, intermédiaire).
// - `tertiary` : correspond à la partie haute de l'écran (proche du zénith, le plus sombre).
// Les pourcentages définissent la répartition verticale des couleurs sur l'écran.

// 🔧 CISCO: TRANSITIONS AVEC VRAIES COULEURS - Ponts naturels entre les modes
// Ces transitions permettent de passer en douceur d'un mode à un autre en utilisant des couleurs intermédiaires.
// Les couleurs sont définies pour trois zones de l'écran :
// - `primary` : correspond à la partie basse de l'écran (proche de l'horizon, le plus clair).
// - `secondary` : correspond à la partie intermédiaire de l'écran (milieu du ciel, intermédiaire).
// - `tertiary` : correspond à la partie haute de l'écran (proche du zénith, le plus sombre).
// Les pourcentages définissent la répartition verticale des couleurs sur l'écran.

// 🔧 CISCO: TRANSITIONS SUPPRIMÉES - Utilisation directe des fonctions spécialisées
// Les transitions avec pont intermédiaire causaient le sursaut orange récalcitrant
// Chaque mode utilise maintenant ses propres couleurs directement

// Interface pour les props du composant
interface DynamicBackgroundProps {
  children: React.ReactNode;
  skyMode?: string; // 🔧 CISCO: Optionnel si contexte disponible
}



const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ children, skyMode }) => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // 🎬 CISCO: SYSTÈME CINÉMATOGRAPHIQUE SIMPLIFIÉ - Props directes uniquement
  const defaultMode = 'dawn';
  const validatedSkyMode = skyMode && typeof skyMode === 'string' ? skyMode : defaultMode;
  const currentModeRef = useRef(validatedSkyMode);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const gradientRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const zoomTimelineRef = useRef<gsap.core.Timeline | null>(null);
  // 🔧 CISCO: sunriseAnimationRef supprimé - géré dans AstronomicalLayer

  // 🔧 CISCO: Background UNIQUE - Background.png seulement (simplification)
  const selectedBackground = '/Background.png'; // Background unique pour simplifier

  // 🔧 CISCO: Position simplifiée pour Background.png unique
  const getBackgroundPosition = (): string => {
    return 'center bottom -220px'; // 🔧 CISCO: Paysage complètement en bas pour MAXIMUM de ciel dégagé et lune visible rapidement
  };
  
  // 🔧 CISCO: SUPPRESSION COMPLÈTE - Plus de fonction automatique basée sur l'heure
  // const getModeForTime = ... // SUPPRIMÉ - Mode manuel uniquement



  // 🔧 CISCO: ANCIEN SYSTÈME SUPPRIMÉ - DiurnalLayer s'occupe maintenant de tout

  // 🔧 CISCO: FONCTION SUPPRIMÉE - applyStarsTransition obsolète
  // Les étoiles sont gérées automatiquement par AstronomicalLayer via props skyMode



  // 🎬 CISCO: LOGIQUE CINÉMATOGRAPHIQUE - DIRECTIONS DÉGRADÉS SELON CYCLE SOLAIRE
  // 🌅 PHASES MONTANTES (Soleil monte) : NUIT → AUBE → dégradés `to top`
  // 🌇 PHASES DESCENDANTES (Soleil descend) : MIDI → COUCHER → dégradés `to bottom`

  const getGradientDirection = (mode: BackgroundMode): string => {
    // 🌅 PHASES MONTANTES - Lumière arrive par l'horizon, monte vers le ciel
    if (['night', 'dawn'].includes(mode)) {
      return 'to top';
    }
    // 🌇 PHASES DESCENDANTES - Lumière part du zénith, descend vers l'horizon
    if (['midday', 'sunset'].includes(mode)) {
      return 'to bottom';
    }
    return 'to top'; // Fallback
  };

  // 🔧 CISCO: FONCTIONS SPÉCIALISÉES avec directions correctes selon logique solaire
  const applyNightMode = () => {
    console.log('🌌 APPLICATION MODE NUIT PROFONDE - Dégradé MONTANT (to top)');
    const colors = BACKGROUND_MODES.night;
    const direction = getGradientDirection('night');
    // 🎨 CISCO: Dégradé qui MONTE - Bleu nuit profond uniforme
    const gradient = `linear-gradient(${direction}, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    const brightness = 0.15;

    if (gradientRef.current) {
      // 🔧 CISCO: NETTOYAGE EXPLICITE - Arrêter toute animation sur cet élément
      gsap.killTweensOf(gradientRef.current);

      // 🔧 CISCO: ANIMATION DOUCE avec nettoyage préalable
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 15.0,
        ease: "power2.inOut",
        force3D: true,
        onStart: () => {
          console.log(`🎨 NUIT: Démarrage transition vers - ${gradient}`);
        },
        onComplete: () => {
          console.log(`🎨 NUIT: Transition terminée - Dégradé nuit appliqué`);
        }
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  const applyDawnMode = () => {
    console.log('🌅 APPLICATION MODE AUBE - DOUBLE DÉGRADÉ - Séquence en deux phases');
    console.log('🔧 CISCO DEBUG: applyDawnMode appelée - Vérification fonctionnement');
    const colors = BACKGROUND_MODES.dawn;
    const direction = getGradientDirection('dawn');

    // 🎨 CISCO: PHASE 1 - Premier dégradé (début d'aube)
    const gradient1 = `linear-gradient(${direction}, ${colors.primary} 0%, ${colors.secondary} 50%, ${colors.tertiary} 100%)`;

    // 🎨 CISCO: PHASE 2 - Deuxième dégradé (progression vers midi)
    // Utilisation de la palette dawnPhase2 définie dans BACKGROUND_MODES
    const dawnPhase2Colors = BACKGROUND_MODES.dawnPhase2;
    const gradient2 = `linear-gradient(${direction}, ${dawnPhase2Colors.primary} 0%, ${dawnPhase2Colors.secondary} 50%, ${dawnPhase2Colors.tertiary} 100%)`;

    const brightness1 = 0.5; // 🔧 CISCO: Luminosité initiale de l'aube
    const brightness2 = 0.7; // 🔧 CISCO: Luminosité progressive vers midi

    if (gradientRef.current) {
      // 🔧 CISCO: NETTOYAGE EXPLICITE - Arrêter toute animation sur cet élément
      gsap.killTweensOf(gradientRef.current);

      // 🎬 CISCO: TIMELINE DOUBLE DÉGRADÉ - Séquence orchestrée
      const dawnTimeline = gsap.timeline({
        onStart: () => {
          console.log(`🎨 AUBE: Démarrage séquence double dégradé`);
        },
        onComplete: () => {
          console.log(`🎨 AUBE: Séquence double dégradé terminée`);
        }
      });

      // 🌅 PHASE 1: Premier dégradé (0-7.5s)
      dawnTimeline.to(gradientRef.current, {
        backgroundImage: gradient1,
        duration: 7.5,
        ease: "power2.inOut",
        force3D: true,
        onStart: () => {
          console.log(`🎨 AUBE PHASE 1: ${gradient1}`);
        },
        onComplete: () => {
          console.log(`🎨 AUBE PHASE 1: Terminée - Transition vers phase 2`);
        }
      });

      // 🌅 PHASE 2: Deuxième dégradé (7.5-15s)
      dawnTimeline.to(gradientRef.current, {
        backgroundImage: gradient2,
        duration: 7.5,
        ease: "power2.inOut",
        force3D: true,
        onStart: () => {
          console.log(`🎨 AUBE PHASE 2: ${gradient2}`);
        },
        onComplete: () => {
          console.log(`🎨 AUBE PHASE 2: Terminée - Prêt pour transition midi`);
        }
      });
    }

    if (landscapeRef.current) {
      // 🔧 CISCO: LUMINOSITÉ PROGRESSIVE synchronisée avec les dégradés
      gsap.killTweensOf(landscapeRef.current);

      const brightnessTimeline = gsap.timeline();

      // Phase 1: Luminosité initiale
      brightnessTimeline.to(landscapeRef.current, {
        filter: `brightness(${brightness1})`,
        duration: 7.5,
        ease: "power2.inOut"
      });

      // Phase 2: Luminosité progressive
      brightnessTimeline.to(landscapeRef.current, {
        filter: `brightness(${brightness2})`,
        duration: 7.5,
        ease: "power2.inOut"
      });
    }
  };

  // 🎬 CISCO: applyMorningMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  // 🔧 CISCO: FONCTION SUPPRIMÉE - resetAllLayers obsolète
  // Utilisation directe des fonctions spécialisées dans setBackgroundMode

  // 🎬 CISCO: applySunriseMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  // 🔧 CISCO: NOUVELLES FONCTIONS POUR PHASES DESCENDANTES
  const applyMiddayMode = () => {
    console.log('🌞 APPLICATION MODE MIDI - CORRECTION CRITIQUE - Couleurs bleues renforcées');
    const colors = BACKGROUND_MODES.midday;
    const direction = getGradientDirection('midday');
    // 🎨 CISCO: CORRECTION - Couleurs bleues renforcées pour éviter blanc cassé
    const gradient = `linear-gradient(${direction}, ${colors.tertiary} 0%, ${colors.secondary} 40%, ${colors.primary} 100%)`;
    const brightness = 1.0; // 🔧 CISCO: Luminosité maximum à midi

    // 🔧 CISCO: DIAGNOSTIC - Vérifier les couleurs utilisées
    console.log(`🎨 MIDI COULEURS: tertiary=${colors.tertiary}, secondary=${colors.secondary}, primary=${colors.primary}`);
    console.log(`🎨 MIDI GRADIENT: ${gradient}`);

    if (gradientRef.current) {
      // 🔧 CISCO: NETTOYAGE EXPLICITE - Arrêter toute animation sur cet élément
      gsap.killTweensOf(gradientRef.current);

      // 🔧 CISCO: ANIMATION DOUCE avec nettoyage préalable
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 20.0, // 🔧 CISCO: Transition plus douce et progressive
        ease: "power1.inOut", // 🔧 CISCO: Easing plus doux pour continuité
        force3D: true,
        onStart: () => {
          console.log(`🎨 MIDI: Démarrage transition vers - ${gradient}`);
        },
        onComplete: () => {
          console.log(`🎨 MIDI: Transition terminée - Dégradé bleu appliqué`);
        }
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  // 🎬 CISCO: applyAfternoonMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  const applySunsetMode = () => {
    console.log('🌇 APPLICATION MODE COUCHER - Continuité douce depuis midi - Réalisme pastel');
    const colors = BACKGROUND_MODES.sunset;
    const direction = getGradientDirection('sunset');
    // 🎨 CISCO: Continuité depuis midi - Bleu ciel en haut, orange pastel au milieu, rouge doux en bas
    const gradient = `linear-gradient(${direction}, ${colors.tertiary} 0%, ${colors.secondary} 60%, ${colors.primary} 100%)`;
    const brightness = 0.85; // 🔧 CISCO: Luminosité douce pour coucher réaliste

    if (gradientRef.current) {
      // 🔧 CISCO: NETTOYAGE EXPLICITE - Arrêter toute animation sur cet élément
      gsap.killTweensOf(gradientRef.current);

      // 🔧 CISCO: ANIMATION DOUCE avec nettoyage préalable
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 18.0, // 🔧 CISCO: Transition plus douce pour continuité
        ease: "power1.inOut", // 🔧 CISCO: Easing plus doux
        force3D: true,
        onStart: () => {
          console.log(`🎨 COUCHER: Démarrage transition vers - ${gradient}`);
        },
        onComplete: () => {
          console.log(`🎨 COUCHER: Transition terminée - Dégradé coucher appliqué`);
        }
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 18.0, // 🔧 CISCO: Synchronisé avec le dégradé
        ease: "power1.inOut" // 🔧 CISCO: Easing plus doux
      });
    }
  };

  // 🎬 CISCO: applyDuskMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  // 🔧 CISCO: Changement de mode avec CROSS FADE progressif TOUJOURS
  const setBackgroundMode = (mode: BackgroundMode) => {
    // 🚀 CISCO: OPTIMISATION - Éviter les transitions inutiles SAUF lors de l'initialisation
    if (currentModeRef.current === mode && !isTransitioning && isInitialized) {
      console.log(`⚡ Mode ${mode} déjà actif, transition ignorée`);
      return;
    }

    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter TOUTES les animations en cours
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation en cours pour nouvelle transition');
      timelineRef.current.kill();
      setIsTransitioning(false); // 🔧 CISCO: Forcer la réinitialisation du flag
    }

    // 🔧 CISCO: NETTOYAGE AGRESSIF - Tuer toutes les animations sur gradientRef
    if (gradientRef.current) {
      gsap.killTweensOf(gradientRef.current);
      console.log('🧹 Nettoyage agressif des animations GSAP sur gradientRef');
    }

    // 🔧 CISCO: PROTECTION ANTI-BLOCAGE - Forcer déblocage après 20s max
    if (isTransitioning) {
      console.log('⏳ Transition en cours, vérification anti-blocage...');
      setTimeout(() => {
        if (isTransitioning) {
          console.log('🔓 DÉBLOCAGE FORCÉ - Transition bloquée > 20s');
          setIsTransitioning(false);
          setBackgroundMode(mode); // Relancer la transition
        }
      }, 20000);
      return;
    }

    // Si c'est le même mode, ne rien faire (évite le spam de logs)
    if (mode === currentModeRef.current) {
      console.log('🔄 Mode identique, pas de transition');
      return;
    }

    console.log(`🎨 Changement de mode vers: ${mode} depuis ${currentModeRef.current}`);

    // 🔧 CISCO: TRANSITION DIRECTE SIMPLIFIÉE - Utiliser les fonctions spécialisées
    // Chaque mode utilise ses vraies couleurs sans transitions intermédiaires
    console.log(`🎯 TRANSITION DIRECTE vers ${mode} - Utilisation fonction spécialisée`);

    // 🌅 CISCO: Appliquer la fonction spécialisée selon le mode avec directions correctes
    switch (mode) {
      // 🎬 CISCO: 4 MODES CINÉMATOGRAPHIQUES SIMPLIFIÉS
      case 'dawn':
        applyDawnMode();
        break;
      case 'midday':
        applyMiddayMode();
        break;
      case 'sunset':
        applySunsetMode();
        break;
      case 'night':
        applyNightMode();
        break;

      default:
        // 🔧 CISCO: MAPPING DES ANCIENS MODES vers les nouveaux modes cinématographiques
        console.warn(`⚠️ Mode non reconnu: ${mode}, mapping vers mode cinématographique`);

        // Mapper les anciens modes vers les 4 modes cinématographiques
        let mappedMode: BackgroundMode;
        switch (mode) {
          case 'sunrise':
          case 'aube':
            mappedMode = 'dawn';
            break;
          case 'morning':
          case 'afternoon':
          case 'midi':
            mappedMode = 'midday';
            break;
          case 'dusk':
          case 'coucher':
            mappedMode = 'sunset';
            break;
          case 'nuit':
            mappedMode = 'night';
            break;
          default:
            console.warn(`🔧 Mode complètement inconnu: ${mode}, utilisation mode par défaut 'dawn'`);
            mappedMode = 'dawn';
            break;
        }

        console.log(`🔄 Mapping ${mode} → ${mappedMode}`);
        setBackgroundMode(mappedMode);
        break;
    }

    // 🔧 CISCO: Mettre à jour currentModeRef APRÈS l'application du mode
    currentModeRef.current = mode;
  };

  // 🔧 CISCO: FONCTION SUPPRIMÉE - updateBackgroundSmoothly obsolète
  // Remplacée par le système de mapping des modes vers les fonctions spécialisées

  // 🔧 CISCO: FONCTION SUPPRIMÉE - updateBackgroundWithBridge obsolète
  // Utilisation des fonctions spécialisées directement (applyDawnMode, applyMiddayMode, etc.)

  // 🔧 CISCO: FONCTION getColorsForMode SUPPRIMÉE - Non utilisée
  // Chaque mode utilise maintenant ses fonctions spécialisées (applyDawnMode, applyMiddayMode, etc.)

  // 🎬 CISCO: FONCTION CINÉMATOGRAPHIQUE SIMPLIFIÉE - 4 modes d'éclairage
  const getBrightnessForMode = (mode: BackgroundMode): number => {
    switch (mode) {
      case 'dawn':
      case 'dawnPhase2': return 0.4;    // Aube : lumière douce progressive
      case 'midday': return 1.0;  // Midi : lumière au maximum
      case 'sunset': return 0.6;  // Coucher : lumière s'atténue
      case 'night': return 0.15;  // Nuit : lumière au minimum
      default: return 0.6;
    }
  };

  // 🔧 CISCO: Fonction de mapping pour les composants enfants
  const mapToValidSkyMode = (mode: BackgroundMode): 'dawn' | 'midday' | 'sunset' | 'night' => {
    switch (mode) {
      case 'dawnPhase2':
      case 'sunrise':
      case 'aube':
        return 'dawn';
      case 'morning':
      case 'afternoon':
      case 'midi':
        return 'midday';
      case 'dusk':
      case 'coucher':
        return 'sunset';
      case 'nuit':
        return 'night';
      case 'dawn':
      case 'midday':
      case 'sunset':
      case 'night':
        return mode;
      default:
        return 'dawn';
    }
  };


  // 🔧 CISCO: FONCTION updateDynamicBackground SUPPRIMÉE - Conflit éliminé
  // Utilisation exclusive de setBackgroundMode() pour éviter les doubles transitions

  // Animation de zoom du paysage
  const createLandscapeZoomAnimation = () => {
    if (!landscapeRef.current) return;
    if (zoomTimelineRef.current) {
      zoomTimelineRef.current.kill();
    }
    zoomTimelineRef.current = gsap.timeline({ repeat: -1, yoyo: false, force3D: true });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 45, ease: "power2.inOut" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 5, ease: "none" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 35, ease: "power2.out" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 10, ease: "none" });
  };

  // 🔧 CISCO: Fonctions soleil supprimées - gérées dans AstronomicalLayer

  // 🌌 CISCO: Fonction SIMPLIFIÉE pour nuit profonde - Dégradé seulement
  const triggerNightAnimation = () => {
    console.log('🌌 DÉCLENCHEMENT NUIT PROFONDE - Dégradé seulement');
    setBackgroundMode('night');
  };

  // Exposer les fonctions globalement
  (window as any).triggerNightAnimation = triggerNightAnimation; // CISCO: Animation nuit profonde

  // 🔧 CISCO: CORRECTION CRITIQUE - Initialisation avec continuité nuit → aube
  useEffect(() => {
    console.log('🎨 INITIALISATION AVEC CONTINUITÉ - Fin de nuit → Début aube');

    createLandscapeZoomAnimation();

    // 🔧 CISCO: CORRECTION CRITIQUE - Appliquer immédiatement les couleurs de fin de nuit
    // pour éviter les couleurs bizarres au démarrage, puis transitionner vers l'aube
    if (gradientRef.current) {
      const nightColors = BACKGROUND_MODES.night;
      const nightDirection = getGradientDirection('night');
      const nightGradient = `linear-gradient(${nightDirection}, ${nightColors.primary} 0%, ${nightColors.secondary} 60%, ${nightColors.tertiary} 100%)`;

      // Appliquer immédiatement les couleurs de nuit (fin du cycle précédent)
      gsap.set(gradientRef.current, {
        backgroundImage: nightGradient
      });
      console.log('🌙 Couleurs de fin de nuit appliquées immédiatement pour continuité');

      // Puis transitionner vers l'aube après un court délai
      setTimeout(() => {
        console.log('🌅 Transition automatique vers l\'aube après initialisation');
        setIsInitialized(true); // Marquer l'initialisation comme terminée
        setBackgroundMode('dawn');
      }, 100); // Court délai pour éviter les conflits
    }

    // 🔧 CISCO: Initialiser l'éclairage du paysage pour la nuit puis transitionner
    if (landscapeRef.current) {
      const nightBrightness = getBrightnessForMode('night');
      gsap.set(landscapeRef.current, {
        filter: `brightness(${nightBrightness})`
      });
      console.log(`💡 Éclairage paysage initialisé pour nuit: brightness(${nightBrightness}) - Continuité assurée`);
    }

    return () => {
      if (timelineRef.current) timelineRef.current.kill();
      if (zoomTimelineRef.current) zoomTimelineRef.current.kill();
    };
  }, []);

  // 🎬 CISCO: SYNCHRONISATION CINÉMATOGRAPHIQUE SIMPLIFIÉE - Props directes uniquement
  useEffect(() => {
    if (skyMode && skyMode !== (currentModeRef.current as string)) {
      // Mode cinématographique via props
      console.log(`🎬 Mode cinématographique: ${skyMode}`);
      setBackgroundMode(skyMode as BackgroundMode);
    } else if (!skyMode) {
      // Mode par défaut si pas de props
      console.log('🌅 Initialisation mode par défaut: dawn (aube)');
      setBackgroundMode(defaultMode as BackgroundMode);
    }
  }, [validatedSkyMode, skyMode]);



  // 🔧 CISCO: Exposer la fonction de changement de mode pour le contrôleur
  useEffect(() => {
    (window as any).setBackgroundMode = (mode: string) => {
      console.log(`🎨 Changement de mode via contrôleur: ${mode}`);
      setBackgroundMode(mode as BackgroundMode);
    };

    return () => {
      delete (window as any).setBackgroundMode;
    };
  }, []);

  return (
    <div
      ref={backgroundRef}
      className="relative overflow-hidden"
      style={{ minHeight: '100vh' }}
    >
      {/* Conteneur pour le dégradé - commence plus haut pour l'aube */}
      <div
        ref={gradientRef}
        className="absolute inset-0"
        style={{
          zIndex: 0,
          backgroundAttachment: 'fixed',
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover'
          // ✅ CORRECTION: Supprimer le fallbackGradient qui entre en conflit avec GSAP
        }}
      />

      {/* Couches avec nuages réduits - Mode synchronisé avec le contexte */}
      <AstronomicalLayer skyMode={mapToValidSkyMode(validatedSkyMode as BackgroundMode)} />
      <DiurnalLayer skyMode={mapToValidSkyMode(validatedSkyMode as BackgroundMode)} />

      {/* 🔧 CISCO: SunriseAnimation déplacé dans AstronomicalLayer */}

      {/* Paysage avec éclairage dynamique - Background aléatoire */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: `url(${selectedBackground})`,
          backgroundPosition: getBackgroundPosition(), // Position pour Background.png
          backgroundSize: 'cover', // Taille standard pour tous les backgrounds
          zIndex: 10, // 🔧 CISCO: Paysage en avant-plan (z-index 10)
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />

      {/* Contenu principal */}
      <div className="relative" style={{ zIndex: 15 }}>
        {children}
      </div>

      {/* 🔧 CISCO: Indicateur de transition SUPPRIMÉ - Plus besoin d'affichage visuel */}
      {/* L'état isTransitioning reste pour la logique interne mais plus d'affichage */}

      <style dangerouslySetInnerHTML={{
        __html: `
          body, html {
            background: none !important;
            background-color: transparent !important;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
