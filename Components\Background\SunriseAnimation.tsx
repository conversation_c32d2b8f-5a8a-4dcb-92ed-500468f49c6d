import { useLayoutEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { gsap } from 'gsap';

// Interface pour les méthodes exposées du composant
export interface SunriseAnimationRef {
  triggerSunrise: () => void;
  triggerMorning: () => void;
  triggerMidday: () => void;
  triggerAfternoon: () => void;
  triggerSunset: () => void;
  triggerDawn: () => void;
  triggerDusk: () => void;
  triggerNight: () => void;
  resetSun: () => void;
  // 🔧 CISCO: Nouvelle méthode pour cycle automatique complet
  startAutomaticCycle: (phaseDuration: number) => void;
  stopAutomaticCycle: () => void;
}

// Interface pour les props du composant
interface SunriseAnimationProps {
  isVisible: boolean;
}

// 🔧 CISCO: POSITIONS SOLAIRES SIMPLIFIÉES - Pourcentages simples
// Système simple : left (0-100%), top (0-100%), 0,0 = coin supérieur gauche
const SUN_POSITIONS = {
  dawn: { left: 10, top: 90 },      // INVISIBLE - Sous horizon à gauche
  sunrise: { left: 20, top: 70 },   // LEVER - Visible sur colline gauche
  morning: { left: 35, top: 40 },   // MATIN - Montée vers centre-gauche
  midday: { left: 50, top: 15 },    // ZÉNITH - Centre-haut de l'écran
  afternoon: { left: 65, top: 40 }, // APRÈS-MIDI - Descente vers centre-droite
  sunset: { left: 80, top: 70 },    // COUCHER - Visible sur colline droite
  dusk: { left: 90, top: 90 },      // INVISIBLE - Sous horizon à droite
  night: { left: 95, top: 95 }      // INVISIBLE - Complètement caché
} as const;

// 🔧 CISCO: SYSTÈME DE CONTINUITÉ SOLEIL/LUNE - ANTI-SAUT BRUTAL
// Sauvegarde des positions entre modes pour continuité parfaite
let savedSunPosition = { left: '10%', top: '90%' }; // Position initiale aube
let savedMoonPosition = { left: '5%', top: '75%', opacity: 0 };

// 🔧 CISCO: FONCTIONS DE SAUVEGARDE/RÉCUPÉRATION POSITION - SYSTÈME SIMPLIFIÉ
const saveSunPosition = (element: HTMLElement) => {
  const currentLeft = gsap.getProperty(element, "left") as string;
  const currentTop = gsap.getProperty(element, "top") as string;
  savedSunPosition = { left: currentLeft, top: currentTop };
  console.log(`💾 POSITION SOLEIL SAUVEGARDÉE: left=${currentLeft}, top=${currentTop}`);
};

const getSavedSunPosition = () => {
  console.log(`📥 RÉCUPÉRATION POSITION SOLEIL: left=${savedSunPosition.left}, top=${savedSunPosition.top}`);
  return savedSunPosition;
};

// 🔧 CISCO: FONCTIONS DE SAUVEGARDE/RÉCUPÉRATION LUNE
const saveMoonPosition = (element: HTMLElement) => {
  const currentX = gsap.getProperty(element, "left") as string;
  const currentY = gsap.getProperty(element, "top") as string;
  const currentOpacity = gsap.getProperty(element, "opacity") as number;
  savedMoonPosition = {
    x: parseFloat(currentX),
    y: parseFloat(currentY),
    opacity: currentOpacity
  };
  console.log(`💾 POSITION LUNE SAUVEGARDÉE: x=${currentX}, y=${currentY}, opacity=${currentOpacity}`);
};

const getSavedMoonPosition = () => {
  console.log(`📥 RÉCUPÉRATION POSITION LUNE: x=${savedMoonPosition.x}, y=${savedMoonPosition.y}, opacity=${savedMoonPosition.opacity}`);
  return savedMoonPosition;
};

// 🔧 CISCO: Fonction de conversion position simple - POURCENTAGES DIRECTS
const getSimplePosition = (positionKey: keyof typeof SUN_POSITIONS) => {
  const pos = SUN_POSITIONS[positionKey];
  return {
    left: `${pos.left}%`,
    top: `${pos.top}%`
  };
};

const SunriseAnimation = forwardRef<SunriseAnimationRef, SunriseAnimationProps>(
  ({ isVisible }, ref) => {
    // Références pour les éléments DOM
    const containerRef = useRef<HTMLDivElement>(null);
    const sunWrapperRef = useRef<HTMLDivElement>(null);
    const sunGlowRef = useRef<HTMLDivElement>(null);
    const lensFlareRef = useRef<HTMLDivElement>(null);
    const sunImageRef = useRef<HTMLImageElement>(null);

    // Référence pour la timeline GSAP
    const timelineRef = useRef<gsap.core.Timeline | null>(null);

    // 🌟 CISCO: Références pour animations continues (lens-flare + halo)
    const continuousAnimationsRef = useRef<(gsap.core.Timeline | gsap.core.Tween)[]>([]);

    // 🔧 CISCO: Références pour le cycle automatique
    const automaticCycleRef = useRef<NodeJS.Timeout | null>(null);
    const currentPhaseIndexRef = useRef(0);

    // 🔧 CISCO: FONCTION UTILITAIRE - Calculer l'intensité du halo selon les spécifications
    const calculateGlowIntensity = (angle: number): number => {
      // SPÉCIFICATIONS CISCO : Invisible (aube/crépuscule/nuit), moitié (lever/coucher), haut (matin/midi/après-midi)
      if (angle < 0) return 0; // INVISIBLE - Sous horizon (aube/crépuscule/nuit)
      if (angle === 0) return 0.5; // MOITIÉ COLLINE - Lever/coucher
      if (angle >= 45) return 1.0; // VISIBLE - Matin/midi/après-midi

      // Interpolation entre 0° et 45°
      const ratio = angle / 45; // 0 à 1
      return 0.5 + (ratio * 0.5); // 0.5 → 1.0
    };

    // 🔧 CISCO: FONCTION UTILITAIRE - Calculer l'intensité des rayons selon les spécifications
    const calculateFlareIntensity = (angle: number): number => {
      // SPÉCIFICATIONS CISCO : Invisible (aube/crépuscule/nuit), moitié (lever/coucher), haut (matin/midi/après-midi)
      if (angle < 0) return 0; // INVISIBLE - Sous horizon (aube/crépuscule/nuit)
      if (angle === 0) return 0.2; // MOITIÉ COLLINE - Lever/coucher (rayons faibles)
      if (angle >= 45) return 0.6; // VISIBLE - Matin/midi/après-midi (rayons forts)

      // Interpolation entre 0° et 45°
      const ratio = angle / 45; // 0 à 1
      return 0.2 + (ratio * 0.4); // 0.2 → 0.6
    };

    // 🌟 CISCO: NOUVELLE FONCTION - Arrêter toutes les animations continues
    const stopContinuousAnimations = () => {
      continuousAnimationsRef.current.forEach(animation => {
        if (animation) animation.kill();
      });
      continuousAnimationsRef.current = [];
    };

    // 🌟 CISCO: NOUVELLE FONCTION - Pulsation subtile du halo lumineux
    const startLuminousHaloPulsation = (intensity: number) => {
      if (!lensFlareRef.current) return;

      // Arrêter les animations précédentes
      stopContinuousAnimations();

      // 🔧 CISCO: Amplitude de pulsation plus visible et réaliste
      const baseOpacity = intensity;
      const maxOpacity = Math.min(1.0, intensity + 0.4); // 🔧 CISCO: Pulsation de +40% (plus visible)
      const pulseDuration = intensity > 0.5 ? 8.0 : 5.0; // 🔧 CISCO: Plus lent et majestueux

      console.log(`🌟 Démarrage pulsation halo - Intensité: ${intensity.toFixed(2)}, Opacité: ${baseOpacity.toFixed(2)}-${maxOpacity.toFixed(2)}, Durée: ${pulseDuration}s`);

      const pulsationAnimation = gsap.timeline({ repeat: -1, yoyo: true });

      pulsationAnimation.to(lensFlareRef.current, {
        opacity: maxOpacity,
        scale: `+=${0.1}`, // 🔧 CISCO: Pulsation de taille aussi (+10%)
        duration: pulseDuration,
        ease: "power1.inOut"
      });

      continuousAnimationsRef.current.push(pulsationAnimation);
    };

    // 🌟 CISCO: NOUVELLE FONCTION - Animation pulsation subtile du glow
    const startGlowPulsation = (baseIntensity: number) => {
      if (!sunGlowRef.current) return;

      // Amplitude de pulsation basée sur l'intensité de base
      const minScale = 0.8 + (baseIntensity * 0.6);
      const maxScale = minScale + 0.15; // Pulsation subtile de 15%
      const pulseDuration = 8.0; // 8 secondes pour une pulsation très douce

      console.log(`💫 Démarrage pulsation halo - Base: ${baseIntensity.toFixed(2)}, Scale: ${minScale.toFixed(2)}-${maxScale.toFixed(2)}`);

      const pulsationAnimation = gsap.timeline({ repeat: -1, yoyo: true });

      pulsationAnimation.to(sunGlowRef.current, {
        scale: maxScale,
        duration: pulseDuration,
        ease: "power1.inOut"
      });

      continuousAnimationsRef.current.push(pulsationAnimation);
    };

    // 🔧 CISCO: FONCTION UTILITAIRE - Calculer la taille du halo selon l'angle
    const calculateHaloSize = (angle: number): number => {
      // Plus le soleil est proche de l'horizon (angle faible), plus le halo est gros
      // Zénith (90°) = petit halo (0.8), Horizon (0°) = gros halo (2.0)
      const normalizedAngle = Math.max(0, Math.min(90, angle)) / 90; // Normaliser 0-1
      const minSize = 2.0; // Taille maximale à l'horizon
      const maxSize = 0.8; // Taille minimale au zénith
      return minSize - (normalizedAngle * (minSize - maxSize));
    };

    // 🔧 CISCO: FONCTION UTILITAIRE - Obtenir les couleurs du halo selon le moment
    const getHaloColors = (position: keyof typeof SUN_POSITIONS) => {
      switch (position) {
        case 'dawn':
          // 🌌 AUBE: Tons bleu-rose très doux
          return {
            inner: 'rgba(230, 243, 255, 0.6)', // Bleu très pâle + rose
            middle: 'rgba(200, 225, 245, 0.4)', // Bleu clair pastel
            outer: 'rgba(176, 212, 241, 0.2)', // Bleu doux
            fade: 'rgba(255, 255, 255, 0.1)',
            transparent: 'rgba(255, 255, 255, 0.05)'
          };

        case 'sunrise':
          // 🌅 LEVER: Tons roses pastels purs
          return {
            inner: 'rgba(255, 228, 225, 0.7)', // Rose pastel très doux
            middle: 'rgba(240, 200, 212, 0.5)', // Rose-bleu pastel
            outer: 'rgba(255, 240, 245, 0.3)', // Rose très pâle
            fade: 'rgba(255, 255, 255, 0.1)',
            transparent: 'rgba(255, 255, 255, 0.05)'
          };

        case 'sunset':
        case 'dusk':
          // 🌇 COUCHER: Tons chauds adoucis (évite le flash orange)
          return {
            inner: 'rgba(255, 220, 180, 0.4)', // Beige chaud doux
            middle: 'rgba(255, 200, 150, 0.3)', // Beige rosé
            outer: 'rgba(255, 240, 200, 0.2)', // Beige très pâle
            fade: 'rgba(255, 255, 255, 0.1)',
            transparent: 'rgba(255, 255, 255, 0.05)'
          };

        case 'midday':
        case 'morning':
        case 'afternoon':
        default:
          // ☀️ ZÉNITH: Blanc lumineux intense
          return {
            inner: 'rgba(255, 255, 255, 0.8)', // Blanc pur intense
            middle: 'rgba(255, 255, 255, 0.6)', // Blanc lumineux
            outer: 'rgba(255, 255, 255, 0.3)', // Blanc diffus
            fade: 'rgba(255, 255, 255, 0.15)',
            transparent: 'rgba(255, 255, 255, 0.08)'
          };
      }
    };

    // 🔧 CISCO: FONCTION UTILITAIRE - Animer le soleil vers une position
    const animateSunToPosition = (
      targetPosition: keyof typeof SUN_POSITIONS,
      duration: number = 360.0, // 🔧 CISCO: RALENTI pour mouvement contemplatif (6 minutes = 360s)
      customGlowIntensity?: number,
      customFlareIntensity?: number
    ) => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn(`☀️ Éléments DOM non prêts pour l'animation ${targetPosition}`);
        return;
      }

      // Calculer l'intensité du halo et des rayons selon la hauteur du soleil
      const angle = SUN_POSITIONS[targetPosition].angle;
      const glowIntensity = customGlowIntensity ?? calculateGlowIntensity(angle);
      const flareIntensity = customFlareIntensity ?? calculateFlareIntensity(angle);

      console.log(`☀️ Animation vers ${targetPosition} - Angle: ${angle}°, Offset: ${SUN_POSITIONS[targetPosition].horizontalOffset}%, Halo: ${glowIntensity.toFixed(2)}, Rayons: ${flareIntensity.toFixed(2)}`);

      // Nettoyer l'animation précédente
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // 🌟 CISCO: Arrêter les animations continues précédentes
      stopContinuousAnimations();

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log(`✨ Animation ${targetPosition} terminée - Position finale: ${angle}°, Halo final: ${glowIntensity.toFixed(2)}, Rayons finaux: ${flareIntensity.toFixed(2)}`);

          // 🌟 CISCO: Démarrer animations continues pour positions avec soleil visible
          if (['morning', 'midday', 'afternoon'].includes(targetPosition) && flareIntensity > 0) {
            console.log(`🌟 Démarrage animations continues pour ${targetPosition}`);
            startLuminousHaloPulsation(flareIntensity);
            startGlowPulsation(glowIntensity);
          }
        }
      });

      // Calculer la position cible
      const targetPos = getSimplePosition(targetPosition);

      console.log(`🎯 Position cible calculée: left=${targetPos.left}, top=${targetPos.top}, Halo adaptatif: ${glowIntensity.toFixed(2)}, Rayons adaptatifs: ${flareIntensity.toFixed(2)}`);

      // PHASE 1: Mouvement du soleil avec courbe parabolique
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          left: targetPos.left,
          top: targetPos.top,
          duration: duration,
          ease: 'power2.inOut' // Courbe naturelle
        },
        0
      );

      // PHASE 2: Animation de la lueur synchronisée
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: glowIntensity,
          scale: 0.8 + (glowIntensity * 0.6), // Scale basé sur l'intensité
          duration: duration * 0.8,
          ease: 'power2.out'
        },
        duration * 0.1 // Démarre après 10% de la durée
      );

      // PHASE 3: Animation du HALO LUMINEUX ADAPTATIF
      // 🔧 CISCO: Calcul de la taille selon position (gros à l'horizon, petit au zénith)
      const haloSize = calculateHaloSize(angle);
      const haloColors = getHaloColors(targetPosition);

      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: flareIntensity,
          scale: haloSize,
          duration: duration * 0.6,
          ease: 'power2.out',
          // 🔧 CISCO: Mise à jour dynamique du gradient selon le moment
          onUpdate: function() {
            if (lensFlareRef.current) {
              lensFlareRef.current.style.background = `
                radial-gradient(circle at center,
                  ${haloColors.inner} 0%,
                  ${haloColors.middle} 20%,
                  ${haloColors.outer} 40%,
                  ${haloColors.fade} 60%,
                  ${haloColors.transparent} 80%,
                  transparent 100%
                )
              `;
            }
          }
        },
        duration * 0.2 // Démarre après 20% de la durée
      );
    };

    // 🌅 CISCO: Animation AUBE - Soleil COMPLÈTEMENT INVISIBLE (caché derrière background)
    const triggerDawn = () => {
      // INVISIBLE : Soleil complètement sous l'horizon, caché derrière le background d'image
      console.log('🌅 AUBE: Soleil complètement invisible, caché derrière le paysage');
      animateSunToPosition('dawn', 360.0, 0, 0); // 🔧 CISCO: RALENTI pour mouvement contemplatif (6min)
      // Masquer complètement le soleil (opacity 0)
      if (sunWrapperRef.current) {
        gsap.to(sunWrapperRef.current, { opacity: 0, duration: 5 });
      }
    };

    // 🌅 CISCO: Animation LEVER DE SOLEIL - MOITIÉ COLLINE (0°)
    const triggerSunrise = () => {
      // MOITIÉ COLLINE : Soleil à moitié visible sur la colline du background
      animateSunToPosition('sunrise', 360.0, undefined, undefined); // 🔧 CISCO: RALENTI pour mouvement contemplatif (6min)
      // Rendre le soleil visible
      if (sunWrapperRef.current) {
        gsap.to(sunWrapperRef.current, { opacity: 1, duration: 5 });
      }
    };

    // 🌄 CISCO: Animation MATIN - MONTE PLUS HAUT avec courbe gauche (45°)
    const triggerMorning = () => {
      // MONTE PLUS HAUT : Soleil monte avec légère courbe vers la gauche
      animateSunToPosition('morning', 360.0, undefined, undefined); // 🔧 CISCO: RALENTI pour mouvement contemplatif (6min)
      // Rendre le soleil visible
      if (sunWrapperRef.current) {
        gsap.to(sunWrapperRef.current, { opacity: 1, duration: 5 });
      }
    };

    // ☀️ CISCO: Animation MIDI/ZÉNITH - CORRECTION CRITIQUE - Progression continue avec descente
    const triggerMidday = () => {
      // ZÉNITH : Continuation naturelle avec descente progressive pour continuité avec coucher
      console.log('☀️ MIDI: CORRECTION CRITIQUE - Progression zénith → début descente pour continuité coucher');

      if (sunWrapperRef.current) {
        // 🔧 CISCO: Récupérer position actuelle pour continuité parfaite (pas de saut)
        const currentX = gsap.getProperty(sunWrapperRef.current, "x");
        const currentY = gsap.getProperty(sunWrapperRef.current, "y");
        console.log(`☀️ Position actuelle récupérée: x=${currentX}, y=${currentY}`);

        // Position zénith (point le plus haut)
        const zenithPos = getSimplePosition('midday');
        // Position début descente (vers afternoon pour continuité)
        const descentPos = getSimplePosition('afternoon');

        console.log(`☀️ Progression MIDI: Zénith (${zenithPos.left}, ${zenithPos.top}) → Début descente (${descentPos.left}, ${descentPos.top})`);

        // 🔧 CISCO: CORRECTION - Animation en 2 phases pour progression continue
        const timeline = gsap.timeline();

        // Phase 1: Montée vers le zénith (première moitié du mode midi)
        timeline.to(sunWrapperRef.current, {
          left: zenithPos.left,
          top: zenithPos.top,
          opacity: 1,
          duration: 180.0, // 🔧 CISCO: RALENTI pour mouvement contemplatif - montée vers zénith (3min)
          ease: 'power1.inOut'
        });

        // Phase 2: Début de descente (deuxième moitié du mode midi)
        timeline.to(sunWrapperRef.current, {
          left: descentPos.left,
          top: descentPos.top,
          duration: 180.0, // 🔧 CISCO: RALENTI pour mouvement contemplatif - début descente (3min)
          ease: 'power1.inOut',
          onComplete: () => {
            // 🔧 CISCO: SAUVEGARDER POSITION pour continuité avec mode COUCHER
            saveSunPosition(sunWrapperRef.current!);
            console.log('☀️ MIDI terminé - Position sauvegardée pour récupération par mode COUCHER');
          }
        });
      }
    };

    // 🌇 CISCO: Animation APRÈS-MIDI - DESCENTE chemin inverse avec courbe droite (45°)
    const triggerAfternoon = () => {
      // DESCENTE : Soleil fait le chemin inverse, descend avec légère courbe vers la droite
      animateSunToPosition('afternoon', 120.0, undefined, undefined); // 🔧 CISCO: RALENTI pour mouvement contemplatif (2min)
      // Rendre le soleil visible
      if (sunWrapperRef.current) {
        gsap.to(sunWrapperRef.current, { opacity: 1, duration: 5 });
      }
    };

    // 🌇 CISCO: Animation COUCHER - SYSTÈME CONTINUITÉ - Récupération position sauvegardée
    const triggerSunset = () => {
      // PHASE COUCHER : Soleil continue sa descente depuis la position sauvegardée du mode MIDI
      console.log('🌇 COUCHER: SYSTÈME CONTINUITÉ - Récupération position sauvegardée depuis MIDI');

      if (sunWrapperRef.current) {
        // 🔧 CISCO: RÉCUPÉRER POSITION SAUVEGARDÉE pour continuité parfaite
        const savedPos = getSavedSunPosition();
        const endPos = getSimplePosition('sunset');

        // 🔧 CISCO: POSITIONNER D'ABORD à la position sauvegardée (pas de saut)
        gsap.set(sunWrapperRef.current, {
          left: savedPos.left,
          top: savedPos.top
        });

        console.log(`🌇 COUCHER: Position récupérée: (${savedPos.left}, ${savedPos.top}) → Horizon (${endPos.left}, ${endPos.top})`);

        // 🔧 CISCO: CONTINUITÉ PARFAITE - Continuer la descente depuis position sauvegardée
        gsap.to(sunWrapperRef.current, {
          left: endPos.left,
          top: endPos.top,
          opacity: 0.8, // Reste visible jusqu'à l'horizon
          duration: 120.0, // 🔧 CISCO: RALENTI pour mouvement contemplatif (2min)
          ease: 'power2.inOut',
          onComplete: () => {
            console.log('🌇 COUCHER terminé - Soleil à l\'horizon pour transition vers NUIT');
          }
        });
      }
    };



    // 🌆 CISCO: Animation CRÉPUSCULE - Soleil MASQUÉ COMPLÈTEMENT (-40°)
    const triggerDusk = () => {
      // 🔧 CISCO: MASQUÉ COMPLÈTEMENT - Soleil bien sous l'horizon
      animateSunToPosition('dusk', 120.0, 0, 0); // 🔧 CISCO: RALENTI pour mouvement contemplatif (2min)
      // 🔧 CISCO: Masquer complètement le soleil (opacity 0 au lieu de 0.3)
      if (sunWrapperRef.current) {
        gsap.to(sunWrapperRef.current, { opacity: 0, duration: 8 });
      }
    };

    // 🌌 CISCO: Animation NUIT PROFONDE - CORRECTION CRITIQUE - Soleil COMPLÈTEMENT INVISIBLE
    const triggerNight = () => {
      // MASQUÉ : Soleil complètement invisible, peut être carrément masqué
      console.log('🌌 NUIT: CORRECTION CRITIQUE - Soleil complètement invisible');
      animateSunToPosition('night', 120.0, 0, 0); // 🔧 CISCO: RALENTI pour mouvement contemplatif (2min)
      // 🔧 CISCO: CORRECTION CRITIQUE - Masquer IMMÉDIATEMENT et COMPLÈTEMENT le soleil
      if (sunWrapperRef.current) {
        gsap.set(sunWrapperRef.current, { opacity: 0 }); // Immédiat
        gsap.to(sunWrapperRef.current, { opacity: 0, duration: 5 }); // + Animation pour sécurité
      }
      // 🔧 CISCO: Masquer aussi le halo et les rayons
      if (sunGlowRef.current) {
        gsap.set(sunGlowRef.current, { opacity: 0 });
      }
      if (lensFlareRef.current) {
        gsap.set(lensFlareRef.current, { opacity: 0 });
      }
    };

    // 🔧 CISCO: CYCLE AUTOMATIQUE COMPLET DU SOLEIL
    const startAutomaticCycle = (phaseDuration: number) => {
      console.log(`🌅 Démarrage du cycle automatique du soleil (${phaseDuration}ms par phase)`);

      // Arrêter le cycle précédent s'il existe
      stopAutomaticCycle();

      // Phases du cycle solaire (4 phases cinématographiques)
      const solarPhases = [
        { name: 'aube', trigger: triggerSunrise },
        { name: 'midi', trigger: triggerMidday },
        { name: 'coucher', trigger: triggerSunset },
        { name: 'nuit', trigger: triggerNight }
      ];

      currentPhaseIndexRef.current = 0;

      const executePhase = () => {
        const currentPhase = solarPhases[currentPhaseIndexRef.current];
        console.log(`☀️ Cycle automatique - Phase ${currentPhaseIndexRef.current + 1}/4: ${currentPhase.name}`);

        // Déclencher l'animation de la phase
        currentPhase.trigger();

        // Programmer la phase suivante
        currentPhaseIndexRef.current = (currentPhaseIndexRef.current + 1) % solarPhases.length;

        automaticCycleRef.current = setTimeout(executePhase, phaseDuration);
      };

      // Démarrer immédiatement la première phase
      executePhase();
    };

    const stopAutomaticCycle = () => {
      if (automaticCycleRef.current) {
        clearTimeout(automaticCycleRef.current);
        automaticCycleRef.current = null;
        console.log('🛑 Cycle automatique du soleil arrêté');
      }
    };

    // 🔄 CISCO: Remettre le soleil en position initiale
    const resetSun = () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // 🌟 CISCO: Arrêter toutes les animations continues
      stopContinuousAnimations();

      if (sunWrapperRef.current && sunGlowRef.current && lensFlareRef.current) {
        // 🔧 CISCO: Position initiale = dawn (aube) - SYNCHRONISÉ avec arrière-plan
        const initialPos = getSimplePosition('dawn');

        gsap.set(sunWrapperRef.current, {
          left: initialPos.left,
          top: initialPos.top,
          opacity: 0 // 🔧 CISCO: Invisible par défaut en dawn (sous horizon)
        });
        gsap.set(sunGlowRef.current, {
          opacity: 0, // 🔧 CISCO: Pas de halo pour dawn (invisible)
          scale: 1.4
        });
        // 🔧 CISCO: Halo adaptatif pour dawn (invisible sous horizon)
        const dawnHaloSize = calculateHaloSize(SUN_POSITIONS.dawn.angle);
        const dawnColors = getHaloColors('dawn');

        gsap.set(lensFlareRef.current, {
          opacity: 0, // 🔧 CISCO: Pas de rayons pour dawn (invisible)
          scale: dawnHaloSize,
          rotation: 0
        });

        // Appliquer les couleurs dawn (invisible)
        if (lensFlareRef.current) {
          lensFlareRef.current.style.background = `
            radial-gradient(circle at center,
              ${dawnColors.inner} 0%,
              ${dawnColors.middle} 20%,
              ${dawnColors.outer} 40%,
              ${dawnColors.fade} 60%,
              ${dawnColors.transparent} 80%,
              transparent 100%
            )
          `;
        }
      }

      console.log('🔄 Soleil remis en position initiale (aube)');
    };

    // Exposer les méthodes via useImperativeHandle
    useImperativeHandle(ref, () => ({
      triggerSunrise,
      triggerMorning,
      triggerMidday,
      triggerAfternoon,
      triggerSunset,
      triggerDawn,
      triggerDusk,
      triggerNight,
      resetSun,
      // 🔧 CISCO: Nouvelles méthodes pour cycle automatique
      startAutomaticCycle,
      stopAutomaticCycle
    }));

    // 🔧 CISCO: Initialisation au montage - Position dawn par défaut - SYNCHRONISÉ
    useLayoutEffect(() => {
      // Positionner le soleil à l'aube dès le démarrage (invisible sous horizon)
      if (sunWrapperRef.current && sunGlowRef.current && lensFlareRef.current) {
        const dawnPos = getSimplePosition('dawn');

        gsap.set(sunWrapperRef.current, {
          left: dawnPos.left,
          top: dawnPos.top,
          opacity: 0 // Invisible à l'aube
        });
        gsap.set(sunGlowRef.current, {
          opacity: 0, // Pas de halo à l'aube
          scale: 1.4
        });
        // 🔧 CISCO: Halo adaptatif pour dawn (invisible sous horizon)
        const dawnHaloSize = calculateHaloSize(SUN_POSITIONS.dawn.angle);
        const dawnColors = getHaloColors('dawn');

        gsap.set(lensFlareRef.current, {
          opacity: 0, // Pas de rayons à l'aube
          scale: dawnHaloSize,
          rotation: 0
        });

        // Appliquer les couleurs dawn (invisible)
        if (lensFlareRef.current) {
          lensFlareRef.current.style.background = `
            radial-gradient(circle at center,
              ${dawnColors.inner} 0%,
              ${dawnColors.middle} 20%,
              ${dawnColors.outer} 40%,
              ${dawnColors.fade} 60%,
              ${dawnColors.transparent} 80%,
              transparent 100%
            )
          `;
        }

        console.log('☀️ Soleil initialisé à l\'aube (dawn) au démarrage - SYNCHRONISÉ');
      }

      return () => {
        if (timelineRef.current) {
          timelineRef.current.kill();
        }
        // 🌟 CISCO: Nettoyer les animations continues
        stopContinuousAnimations();
        // 🔧 CISCO: Nettoyer le cycle automatique
        stopAutomaticCycle();
      };
    }, []);

    // Ne pas rendre si non visible
    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={containerRef}
        className="fixed inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 6 }} // 🔧 CISCO: Z-index 6 - DERRIÈRE les nuages (z-index 9)
      >
        {/* Conteneur pour le soleil et ses effets */}
        <div
          ref={sunWrapperRef}
          className="absolute w-24 h-24 right-0 top-0"
          style={{
            transform: 'translate(-50%, -50%)', // 🔧 CISCO: Point de référence coin supérieur droit
            opacity: 0, // 🔧 CISCO: CORRECTION CRITIQUE - Invisible par défaut pour éviter apparition en nuit
          }}
        >
          <div className="relative w-full h-full">
            {/* EFFET 1: Halo blanc du soleil - Corrigé selon spécifications Cisco */}
            <div
              ref={sunGlowRef}
              className="absolute opacity-0"
              style={{
                width: '150px',
                height: '150px',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                background: `
                  radial-gradient(circle at center,
                    rgba(255, 255, 255, 0.8) 0%,
                    rgba(255, 255, 255, 0.4) 40%,
                    rgba(255, 255, 255, 0.1) 70%,
                    transparent 100%
                  )
                `,
                borderRadius: '50%',
                filter: 'blur(8px)',
                zIndex: 7 // 🔧 CISCO: Halo blanc DERRIÈRE les nuages (z-index 9)
              }}
            />
            
            {/* 🌟 CISCO: NOUVEAU SOLEIL EN CODE - Remplacement des images PNG */}

            {/* EFFET 2: Soleil principal - Cercle avec gradient radial */}
            <div
              ref={sunImageRef}
              className="absolute inset-0 w-full h-full"
              style={{
                background: `
                  radial-gradient(circle at center,
                    #ffffff 0%,
                    #fffef8 15%,
                    #fffcf0 30%,
                    #fff8e1 50%,
                    #fff3d3 70%,
                    #ffefc4 100%
                  )
                `,
                borderRadius: '50%',
                boxShadow: `
                  0 0 30px rgba(255, 255, 255, 1.0),
                  0 0 60px rgba(255, 235, 153, 0.9),
                  0 0 90px rgba(255, 215, 0, 0.7),
                  0 0 120px rgba(255, 200, 0, 0.5)
                `,
                zIndex: 7, // 🔧 CISCO: Soleil DERRIÈRE les nuages (z-index 9)
                opacity: 1
              }}
            />

            {/* EFFET 3: HALO LUMINEUX ADAPTATIF - Remplace le lens-flare */}
            <div
              ref={lensFlareRef}
              className="absolute opacity-0 pointer-events-none"
              style={{
                width: '300px', // 🔧 CISCO: Taille de base plus grande pour plus d'impact
                height: '300px',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                transformOrigin: 'center center',
                zIndex: 8, // 🔧 CISCO: Halo soleil DERRIÈRE les nuages (z-index 9)
                background: `
                  radial-gradient(circle at center,
                    rgba(255, 255, 255, 0.6) 0%,
                    rgba(255, 255, 255, 0.4) 20%,
                    rgba(255, 255, 255, 0.2) 40%,
                    rgba(255, 255, 255, 0.1) 60%,
                    rgba(255, 255, 255, 0.05) 80%,
                    transparent 100%
                  )
                `,
                borderRadius: '50%',
                filter: 'blur(30px)', // 🔧 CISCO: Effet de diffusion lumineux intense
              }}
            />
          </div>
        </div>
      </div>
    );
  }
);

SunriseAnimation.displayName = 'SunriseAnimation';

export default SunriseAnimation;
